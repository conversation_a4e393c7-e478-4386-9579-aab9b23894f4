import type { Service } from '../types';

// Service configuration with translation keys
export const serviceConfigs: Array<{
  id: string;
  translationKey: string;
  icon: string;
  color: 'red' | 'orange' | 'blue' | 'purple' | 'gray';
}> = [
  {
    id: 'lead-generation',
    translationKey: 'leadGeneration',
    icon: 'Target',
    color: 'red'
  },
  {
    id: 'cross-selling-upselling',
    translationKey: 'crossSellingUpselling',
    icon: 'TrendingUp',
    color: 'orange'
  },
  {
    id: 'appointment-scheduling',
    translationKey: 'appointmentScheduling',
    icon: 'Calendar',
    color: 'blue'
  },
  {
    id: 'calendar-management',
    translationKey: 'calendarManagement',
    icon: 'Clock',
    color: 'blue'
  },
  {
    id: 'retention-calls',
    translationKey: 'retentionCalls',
    icon: 'Heart',
    color: 'purple'
  },
  {
    id: 'customer-satisfaction-research',
    translationKey: 'customerSatisfactionResearch',
    icon: 'BarChart3',
    color: 'orange'
  },
  {
    id: 'inbound-telemarketing',
    translationKey: 'inboundTelemarketing',
    icon: 'Phone',
    color: 'red'
  }
];

// Function to get services with translations
export const getServicesWithTranslations = (t: (key: string) => any): Service[] => {
  return serviceConfigs.map(config => ({
    id: config.id,
    title: t(`servicesData.${config.translationKey}.title`),
    description: t(`servicesData.${config.translationKey}.description`),
    benefits: t(`servicesData.${config.translationKey}.benefits`),
    icon: config.icon,
    color: config.color
  }));
};
