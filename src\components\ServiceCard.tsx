import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Target,
  TrendingUp,
  Calendar,
  Heart,
  Phone,
  BarChart3,
  Clock,
  CheckCircle
} from 'lucide-react';
import type { Service } from '../types';

interface ServiceCardProps {
  service: Service;
}

const iconMap = {
  Target,
  TrendingUp,
  Calendar,
  Heart,
  Phone,
  BarChart3,
  Clock,
};

const colorMap = {
  red: {
    bg: 'bg-nova-red',
    text: 'text-nova-red',
    border: 'border-nova-red',
    hover: 'hover:border-nova-red'
  },
  orange: {
    bg: 'bg-nova-orange',
    text: 'text-nova-orange',
    border: 'border-nova-orange',
    hover: 'hover:border-nova-orange'
  },
  blue: {
    bg: 'bg-nova-blue',
    text: 'text-nova-blue',
    border: 'border-nova-blue',
    hover: 'hover:border-nova-blue'
  },
  purple: {
    bg: 'bg-nova-purple',
    text: 'text-nova-purple',
    border: 'border-nova-purple',
    hover: 'hover:border-nova-purple'
  },
  gray: {
    bg: 'bg-nova-gray',
    text: 'text-nova-gray',
    border: 'border-nova-gray',
    hover: 'hover:border-nova-gray'
  }
};

const ServiceCard: React.FC<ServiceCardProps> = ({ service }) => {
  const { t } = useTranslation();
  const IconComponent = iconMap[service.icon as keyof typeof iconMap];
  const colors = colorMap[service.color];

  return (
    <div className={`bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border-2 border-gray-100 ${colors.hover} group`}>
      {/* Icon */}
      <div className={`w-16 h-16 ${colors.bg} rounded-lg flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
        {IconComponent && <IconComponent className="text-white" size={32} />}
      </div>

      {/* Title */}
      <h3 className="text-xl font-bold text-gray-900 mb-4">
        {service.title}
      </h3>

      {/* Description */}
      <p className="text-gray-600 mb-6 leading-relaxed">
        {service.description}
      </p>

      {/* Benefits */}
      <div className="space-y-3">
        <h4 className={`font-semibold ${colors.text} mb-3`}>{t('serviceCard.keyBenefits')}</h4>
        <ul className="space-y-2">
          {service.benefits.map((benefit, index) => (
            <li key={index} className="flex items-start">
              <CheckCircle className={`${colors.text} mr-2 mt-0.5 flex-shrink-0`} size={16} />
              <span className="text-gray-700 text-sm">{benefit}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* Call to Action */}
      <div className="mt-6 pt-6 border-t border-gray-100">
        <button className={`w-full ${colors.bg} text-white py-3 px-4 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-medium`}>
          {t('serviceCard.learnMore')}
        </button>
      </div>
    </div>
  );
};

export default ServiceCard;
